"""
OpenVoice V2 Voice Cloning Backend - Enhanced Version
Fixes all three issues: model downloads, voice quality, and TTS generation
"""

import os
import sys
import warnings
import tempfile
import uuid
import shutil
from pathlib import Path
from typing import Optional, Dict, Tuple
import json

warnings.filterwarnings("ignore")

# FastAPI imports
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Audio processing
import torch
import torchaudio
import numpy as np
import librosa
import soundfile as sf

# Fix for numpy compatibility issue with older libraries
if not hasattr(np, 'float'):
    np.float = np.float64

# Add OpenVoice to path
openvoice_path = Path(__file__).parent / "models" / "openvoice" / "OpenVoice"
sys.path.append(str(openvoice_path))

# Import OpenVoice V2 components
try:
    from openvoice import se_extractor
    from openvoice.api import ToneColorConverter
    from melo.api import TTS
    print("✅ OpenVoice V2 imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure OpenVoice V2 and MeloTTS are installed")
    sys.exit(1)

# Initialize FastAPI app
app = FastAPI(
    title="OpenVoice V2 Voice Cloning API",
    description="Ultra high-fidelity voice cloning with OpenVoice V2",
    version="2.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
tone_color_converter: Optional[ToneColorConverter] = None
tts_models: Dict[str, TTS] = {}  # Cache TTS models
# Force CPU mode to avoid CUDA driver issues
device: str = "cpu"  # Using CPU to avoid CUDA driver version conflicts
current_speaker_embedding = None
current_voice_info = None
audio_storage: Dict[str, str] = {}

# Enhanced audio processor
class EnhancedAudioProcessor:
    """Enhanced audio processing for better voice quality"""
    
    @staticmethod
    def enhance_audio(audio, sr, method='spectral'):
        """Apply spectral enhancement for clearer voice"""
        # Spectral subtraction for noise reduction
        stft = librosa.stft(audio)
        magnitude = np.abs(stft)
        phase = np.angle(stft)
        
        # Estimate noise floor from quietest parts
        noise_floor = np.percentile(magnitude, 10)
        
        # Spectral gating with smooth transition
        gate_threshold = noise_floor * 2.5
        smoothing = 0.95
        magnitude_smoothed = magnitude.copy()
        
        for i in range(1, magnitude.shape[1]):
            magnitude_smoothed[:, i] = (smoothing * magnitude_smoothed[:, i-1] + 
                                       (1 - smoothing) * magnitude[:, i])
        
        # Apply gating
        mask = magnitude_smoothed > gate_threshold
        magnitude_gated = magnitude * mask
        
        # Reconstruct with enhanced magnitude
        enhanced_stft = magnitude_gated * np.exp(1j * phase)
        enhanced_audio = librosa.istft(enhanced_stft)
        
        # Apply gentle compression
        enhanced_audio = np.tanh(enhanced_audio * 0.8) / 0.8
        
        return enhanced_audio

# Pydantic models
class AdvancedSettings(BaseModel):
    temperature: float = 0.3
    length_penalty: float = 1.0
    repetition_penalty: float = 5.0
    top_k: int = 20
    top_p: float = 0.9
    voice_stability: float = 0.85
    emotion_strength: float = 0.8
    noise_scale: float = 0.333
    noise_scale_w: float = 0.6

class TTSRequest(BaseModel):
    text: str
    quality_mode: str = "high"
    remove_silence: bool = False
    language: str = "english"
    speed: float = 1.0
    advanced_settings: Optional[AdvancedSettings] = None

class VoiceCloneResponse(BaseModel):
    success: bool
    message: str
    voice_info: Optional[str] = None
    sample_audio_id: Optional[str] = None

class TTSResponse(BaseModel):
    success: bool
    message: str
    audio_id: Optional[str] = None

# Language mapping with proper codes
LANGUAGE_MAP = {
    'english': 'EN_NEWEST',
    'spanish': 'ES',
    'french': 'FR',
    'chinese': 'ZH',
    'japanese': 'JP',
    'korean': 'KR'
}

@app.on_event("startup")
async def startup_event():
    """Initialize OpenVoice V2 and preload models"""
    global tone_color_converter, tts_models
    
    print("🚀 Initializing OpenVoice V2 System...")
    
    try:
        # Initialize tone color converter
        converter_path = Path(__file__).parent / "models" / "openvoice" / "checkpoints_v2" / "converter"
        config_path = converter_path / "config.json"
        checkpoint_path = converter_path / "checkpoint.pth"
        
        if not config_path.exists() or not checkpoint_path.exists():
            raise FileNotFoundError(
                "OpenVoice V2 models not found. Please run download_openvoice_v2.py"
            )
        
        tone_color_converter = ToneColorConverter(str(config_path), device=device)
        tone_color_converter.load_ckpt(str(checkpoint_path))
        
        print(f"✅ Tone color converter initialized on {device}")
        
        # Preload English TTS model to avoid first-run downloads
        print("📦 Preloading English TTS model...")
        tts_models['EN_NEWEST'] = TTS(language='EN_NEWEST', device=device)
        print("✅ English TTS model loaded")
        
        # Optional: Preload other language models
        # for lang, code in LANGUAGE_MAP.items():
        #     if code not in tts_models:
        #         print(f"📦 Preloading {lang} TTS model...")
        #         tts_models[code] = TTS(language=code, device=device)
        
        print("✅ OpenVoice V2 system fully initialized!")
        
    except Exception as e:
        print(f"❌ Failed to initialize OpenVoice V2: {e}")
        raise

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "OpenVoice V2 API is running!",
        "version": "2.1.0",
        "device": device,
        "models_loaded": list(tts_models.keys())
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for frontend"""
    return {
        "status": "healthy",
        "message": "OpenVoice V2 API is running!",
        "device": device,
        "tone_converter_loaded": tone_color_converter is not None,
        "tts_models_loaded": len(tts_models),
        "languages_available": list(LANGUAGE_MAP.keys()),
        "voice_system_ready": tone_color_converter is not None  # Frontend expects this field
    }

def process_reference_audio(audio_path: str, enhance: bool = True) -> str:
    """Process reference audio with enhanced quality"""
    print(f"📊 Processing reference audio: {audio_path}")
    
    # Load audio
    audio, sr = librosa.load(audio_path, sr=24000, mono=True)
    
    # Normalize
    audio = audio / np.max(np.abs(audio) + 1e-7)
    
    # Apply voice activity detection
    intervals = librosa.effects.split(audio, top_db=20)
    audio_vad = []
    for start, end in intervals:
        audio_vad.extend(audio[start:end])
    
    if len(audio_vad) > 0:
        audio = np.array(audio_vad)
    
    # Apply enhancement if requested
    if enhance:
        processor = EnhancedAudioProcessor()
        audio = processor.enhance_audio(audio, sr)
    
    # Save processed audio
    output_path = tempfile.mktemp(suffix='_processed.wav')
    sf.write(output_path, audio, sr)
    
    print(f"✅ Audio processed: {len(audio)/sr:.1f}s")
    return output_path

def extract_speaker_embedding(audio_path: str) -> Tuple[torch.Tensor, dict]:
    """Extract high-quality speaker embedding"""
    print("🎯 Extracting speaker embedding...")
    
    try:
        # Use OpenVoice V2's se_extractor with enhanced settings
        speaker_embedding, audio_name = se_extractor.get_se(
            audio_path,
            tone_color_converter,
            vad=True,
            target_dir='processed_voices'
        )

        # Ensure embedding is on correct device
        if device == "cuda:0" and torch.cuda.is_available():
            speaker_embedding = speaker_embedding.cuda()
        else:
            speaker_embedding = speaker_embedding.cpu()

        # Normalize embedding for better quality
        speaker_embedding = torch.nn.functional.normalize(speaker_embedding, dim=0)

        print("✅ High-quality speaker embedding extracted")
        return speaker_embedding, {"quality": "enhanced", "device": device, "audio_name": audio_name}
        
    except Exception as e:
        print(f"⚠️ Enhanced extraction failed: {e}, using fallback")
        # Fallback method
        speaker_embedding, audio_name = se_extractor.get_se(
            audio_path,
            tone_color_converter,
            vad=False
        )

        # Ensure embedding is on correct device
        if device == "cuda:0" and torch.cuda.is_available():
            speaker_embedding = speaker_embedding.cuda()
        else:
            speaker_embedding = speaker_embedding.cpu()

        return speaker_embedding, {"quality": "standard", "device": device, "audio_name": audio_name}

def generate_voice_sample(speaker_embedding: torch.Tensor) -> Optional[str]:
    """Generate a voice sample using the cloned voice"""
    sample_text = "Your voice has been successfully cloned with ultra high quality. This is how I sound with your voice characteristics."
    
    try:
        # Generate using enhanced settings
        output_path = generate_speech_enhanced(
            text=sample_text,
            speaker_embedding=speaker_embedding,
            language='EN_NEWEST',
            speed=1.0,
            quality="ultra"
        )
        
        return output_path
        
    except Exception as e:
        print(f"⚠️ Sample generation failed: {e}")
        return None

def generate_speech_enhanced(
    text: str,
    speaker_embedding: torch.Tensor,
    language: str = 'EN_NEWEST',
    speed: float = 1.0,
    remove_silence: bool = False,
    quality: str = "high",
    advanced_settings: Optional[AdvancedSettings] = None
) -> str:
    """Generate ultra high-quality speech using OpenVoice V2's two-stage process"""
    print(f"🎙️ Generating enhanced speech: '{text[:50]}...'")
    
    # Ensure TTS model is loaded
    if language not in tts_models:
        print(f"📦 Loading {language} TTS model...")
        tts_models[language] = TTS(language=language, device=device)
    
    model = tts_models[language]
    
    # Stage 1: Generate base TTS with enhanced parameters
    print("📢 Stage 1: Generating high-quality base TTS...")
    speaker_ids = model.hps.data.spk2id
    speaker_key = list(speaker_ids.keys())[0]
    speaker_id = speaker_ids[speaker_key]
    
    # Apply advanced settings to base generation
    if quality == "ultra" and advanced_settings:
        # Modify TTS parameters for ultra quality
        original_noise_scale = getattr(model.hps.data, 'noise_scale', 0.667)
        original_noise_scale_w = getattr(model.hps.data, 'noise_scale_w', 0.8)
        
        model.hps.data.noise_scale = advanced_settings.noise_scale
        model.hps.data.noise_scale_w = advanced_settings.noise_scale_w
    
    # Generate base audio
    temp_base = tempfile.mktemp(suffix='_base.wav')
    model.tts_to_file(text, speaker_id, temp_base, speed=speed)
    
    # Restore original parameters if modified
    if quality == "ultra" and advanced_settings:
        model.hps.data.noise_scale = original_noise_scale
        model.hps.data.noise_scale_w = original_noise_scale_w
    
    # Stage 2: Apply enhanced tone color conversion
    print("🎨 Stage 2: Applying enhanced voice characteristics...")
    
    # Get source speaker embedding
    base_speaker_key = speaker_key.lower().replace('_', '-')
    source_se_path = Path(__file__).parent / "models" / "openvoice" / "checkpoints_v2" / "base_speakers" / "ses" / f"{base_speaker_key}.pth"
    
    # Handle path variations
    if not source_se_path.exists():
        # Try alternative paths
        alt_paths = [
            f"en-newest.pth",
            f"en-default.pth",
            f"en-us.pth",
            f"en.pth"
        ]
        for alt in alt_paths:
            alt_path = source_se_path.parent / alt
            if alt_path.exists():
                source_se_path = alt_path
                break
    
    if not source_se_path.exists():
        print(f"⚠️ Source SE not found at {source_se_path}, extracting from base audio")
        source_se, _ = se_extractor.get_se(temp_base, tone_color_converter, vad=False)
    else:
        source_se = torch.load(str(source_se_path), map_location=device)
    
    # Ensure embeddings are on the same device
    if device == "cuda:0" and torch.cuda.is_available():
        source_se = source_se.cuda()
        if not speaker_embedding.is_cuda:
            speaker_embedding = speaker_embedding.cuda()
    else:
        source_se = source_se.cpu()
        if speaker_embedding.is_cuda:
            speaker_embedding = speaker_embedding.cpu()
    
    # Apply tone color conversion with enhanced settings
    output_path = tempfile.mktemp(suffix='_final.wav')
    
    # Enhanced conversion parameters
    tau = 0.3 if quality == "ultra" else 0.5  # Lower tau for more precise conversion
    
    tone_color_converter.convert(
        audio_src_path=temp_base,
        src_se=source_se,
        tgt_se=speaker_embedding,
        output_path=output_path,
        message="@OpenVoiceV2",
        tau=tau
    )
    
    # Cleanup base audio
    os.remove(temp_base)
    
    # Post-process for ultra quality
    if quality == "ultra":
        audio, sr = librosa.load(output_path, sr=None)
        
        # Apply final enhancement
        processor = EnhancedAudioProcessor()
        audio = processor.enhance_audio(audio, sr)
        
        # Remove silence if requested
        if remove_silence:
            audio, _ = librosa.effects.trim(audio, top_db=20)
        
        # Save enhanced audio
        sf.write(output_path, audio, sr)
    elif remove_silence:
        # Just remove silence for standard quality
        audio, sr = librosa.load(output_path, sr=None)
        audio, _ = librosa.effects.trim(audio, top_db=20)
        sf.write(output_path, audio, sr)
    
    print("✅ Ultra high-quality speech generation complete")
    return output_path

@app.post("/clone-voice", response_model=VoiceCloneResponse)
async def clone_voice(
    audio_file: UploadFile = File(...),
    enhancement_level: str = Form("gentle")
):
    """Clone a voice with enhanced quality"""
    global current_speaker_embedding, current_voice_info
    
    print(f"\n🎯 Voice cloning request: {audio_file.filename}")
    print(f"Enhancement level: {enhancement_level}")
    
    if not tone_color_converter:
        raise HTTPException(status_code=500, detail="System not initialized")
    
    try:
        # Save uploaded file
        temp_input = tempfile.mktemp(suffix='.wav')
        with open(temp_input, 'wb') as f:
            content = await audio_file.read()
            f.write(content)
        
        # Process audio with enhancement
        enhance = enhancement_level in ["minimal", "gentle"]
        processed_path = process_reference_audio(temp_input, enhance=enhance)
        
        # Extract high-quality speaker embedding
        speaker_embedding, embedding_info = extract_speaker_embedding(processed_path)
        
        # Store for later use
        current_speaker_embedding = speaker_embedding
        
        # Get audio info
        audio, sr = librosa.load(processed_path, sr=None)
        duration = len(audio) / sr
        current_voice_info = f"Voice cloned | {duration:.1f}s | Ultra Quality | Ready for all languages"
        
        # Generate ultra high-quality sample
        sample_path = generate_voice_sample(speaker_embedding)
        
        # Cleanup
        if processed_path != temp_input:
            os.remove(processed_path)
        os.remove(temp_input)
        
        if sample_path:
            # Store sample audio
            audio_id = str(uuid.uuid4())
            audio_storage[audio_id] = sample_path
            
            return VoiceCloneResponse(
                success=True,
                message="Voice cloned successfully with ultra high quality!",
                voice_info=current_voice_info,
                sample_audio_id=audio_id
            )
        else:
            return VoiceCloneResponse(
                success=True,
                message="Voice cloned (sample generation failed)",
                voice_info=current_voice_info,
                sample_audio_id=None
            )
            
    except Exception as e:
        print(f"❌ Voice cloning error: {e}")
        import traceback
        traceback.print_exc()
        
        # Cleanup on error
        if 'temp_input' in locals() and os.path.exists(temp_input):
            os.remove(temp_input)
            
        raise HTTPException(status_code=500, detail=f"Voice cloning failed: {str(e)}")

@app.post("/generate-tts", response_model=TTSResponse)
async def generate_tts(request: TTSRequest):
    """Generate ultra high-quality text-to-speech"""
    global current_speaker_embedding
    
    print(f"\n🎙️ TTS request: '{request.text[:50]}...'")
    
    # Fix: Check if speaker embedding exists properly
    if current_speaker_embedding is None:
        raise HTTPException(status_code=400, detail="Please clone a voice first")
    
    if not request.text.strip():
        raise HTTPException(status_code=400, detail="Text cannot be empty")
    
    try:
        # Map language
        lang_code = LANGUAGE_MAP.get(request.language.lower(), 'EN_NEWEST')
        
        # Determine quality level
        quality = "ultra" if request.quality_mode == "high" else "standard"
        
        # Generate speech with enhanced quality
        output_path = generate_speech_enhanced(
            text=request.text,
            speaker_embedding=current_speaker_embedding,
            language=lang_code,
            speed=request.speed,
            remove_silence=request.remove_silence,
            quality=quality,
            advanced_settings=request.advanced_settings
        )
        
        # Store audio
        audio_id = str(uuid.uuid4())
        audio_storage[audio_id] = output_path
        
        return TTSResponse(
            success=True,
            message="Ultra high-quality speech generated successfully!",
            audio_id=audio_id
        )
        
    except Exception as e:
        print(f"❌ TTS generation error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")

@app.get("/audio/{audio_id}")
async def get_audio(audio_id: str):
    """Serve generated audio files"""
    if audio_id not in audio_storage:
        raise HTTPException(status_code=404, detail="Audio not found")
    
    audio_path = audio_storage[audio_id]
    if not os.path.exists(audio_path):
        raise HTTPException(status_code=404, detail="Audio file not found")
    
    return FileResponse(
        audio_path,
        media_type="audio/wav",
        filename=f"openvoice_v2_{audio_id}.wav"
    )

@app.get("/languages")
async def get_languages():
    """Get supported languages"""
    return {
        "languages": [
            {"code": "english", "name": "English", "model": "EN_NEWEST"},
            {"code": "spanish", "name": "Spanish", "model": "ES"},
            {"code": "french", "name": "French", "model": "FR"},
            {"code": "chinese", "name": "Chinese", "model": "ZH"},
            {"code": "japanese", "name": "Japanese", "model": "JP"},
            {"code": "korean", "name": "Korean", "model": "KR"}
        ]
    }

@app.delete("/clear-voice")
async def clear_voice():
    """Clear current voice"""
    global current_speaker_embedding, current_voice_info
    current_speaker_embedding = None
    current_voice_info = None
    return {"success": True, "message": "Voice cleared"}

if __name__ == "__main__":
    import uvicorn
    print("\n🚀 Starting OpenVoice V2 Ultra Quality Server...")
    print(f"🔧 Device: {device}")
    print("📡 API: http://localhost:8000")
    print("📚 Docs: http://localhost:8000/docs\n")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)